{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://hotpreview.dev/schemas/cpp-protocol.json", "title": "Component Preview Protocol (CPP) Schema", "description": "JSON Schema for the Component Preview Protocol - a cross-platform UI component preview system protocol", "type": "object", "$defs": {"jsonrpc-request": {"type": "object", "description": "JSON-RPC 2.0 request object", "properties": {"jsonrpc": {"type": "string", "const": "2.0", "description": "JSON-RPC protocol version"}, "id": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "null"}], "description": "Request identifier"}, "method": {"type": "string", "description": "Method name to invoke"}, "params": {"type": "object", "description": "Method parameters"}}, "required": ["jsonrpc", "method"], "additionalProperties": false}, "jsonrpc-response": {"type": "object", "description": "JSON-RPC 2.0 response object", "properties": {"jsonrpc": {"type": "string", "const": "2.0", "description": "JSON-RPC protocol version"}, "id": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "null"}], "description": "Request identifier that this response corresponds to"}, "result": {"description": "Method result (present on success)"}, "error": {"type": "object", "description": "Error object (present on failure)", "properties": {"code": {"type": "integer", "description": "Error code"}, "message": {"type": "string", "description": "Error message"}, "data": {"description": "Additional error data"}}, "required": ["code", "message"], "additionalProperties": false}}, "required": ["jsonrpc", "id"], "oneOf": [{"required": ["result"]}, {"required": ["error"]}], "additionalProperties": false}, "UIComponentInfo": {"type": "object", "description": "Information about a UI component and its available previews", "properties": {"name": {"type": "string", "description": "Fully qualified component name (e.g., MyApp.Views.ProductCard)", "minLength": 1, "maxLength": 500}, "uiComponentKind": {"type": "string", "enum": ["page", "control", "unknown"], "description": "Type of UI component"}, "displayName": {"type": ["string", "null"], "description": "Optional human-readable display name override", "maxLength": 200}, "previews": {"type": "array", "description": "Array of available preview configurations for this component", "items": {"$ref": "#/$defs/PreviewInfo"}}}, "required": ["name", "uiComponentKind", "previews"], "additionalProperties": false}, "PreviewInfo": {"type": "object", "description": "Information about a specific preview configuration", "properties": {"previewType": {"type": "string", "enum": ["class", "staticMethod"], "description": "Type of preview implementation"}, "name": {"type": "string", "description": "Preview identifier/name", "minLength": 1, "maxLength": 200}, "displayName": {"type": ["string", "null"], "description": "Optional human-readable display name override", "maxLength": 200}, "autoGenerated": {"type": "boolean", "description": "Whether this preview was automatically generated by the system"}}, "required": ["previewType", "name", "autoGenerated"], "additionalProperties": false}, "PreviewCommandInfo": {"type": "object", "description": "Information about an executable preview command", "properties": {"name": {"type": "string", "description": "Fully qualified command name (e.g., MyApp.Commands.RefreshData)", "minLength": 1, "maxLength": 500}, "displayName": {"type": ["string", "null"], "description": "Optional human-readable display name override", "maxLength": 200}}, "required": ["name"], "additionalProperties": false}, "components-list-request": {"allOf": [{"$ref": "#/$defs/jsonrpc-request"}, {"properties": {"method": {"const": "components/list"}, "params": {"type": "object", "additionalProperties": false}}, "required": ["method"]}], "examples": [{"jsonrpc": "2.0", "id": 1, "method": "components/list", "params": {}}]}, "components-list-response": {"allOf": [{"$ref": "#/$defs/jsonrpc-response"}, {"properties": {"result": {"type": "array", "items": {"$ref": "#/$defs/UIComponentInfo"}}}}], "examples": [{"jsonrpc": "2.0", "id": 1, "result": [{"name": "MyApp.Views.ProductCard", "uiComponentKind": "control", "displayName": "Product Card", "previews": [{"previewType": "staticMethod", "name": "DefaultPreview", "displayName": "<PERSON><PERSON><PERSON>", "autoGenerated": false}]}]}]}, "components-get-request": {"allOf": [{"$ref": "#/$defs/jsonrpc-request"}, {"properties": {"method": {"const": "components/get"}, "params": {"type": "object", "properties": {"componentName": {"type": "string", "description": "Fully qualified name of the component to retrieve", "minLength": 1}}, "required": ["componentName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 2, "method": "components/get", "params": {"componentName": "MyApp.Views.ProductCard"}}]}, "components-get-response": {"allOf": [{"$ref": "#/$defs/jsonrpc-response"}, {"properties": {"result": {"oneOf": [{"$ref": "#/$defs/UIComponentInfo"}, {"type": "null"}]}}}], "examples": [{"jsonrpc": "2.0", "id": 2, "result": {"name": "MyApp.Views.ProductCard", "uiComponentKind": "control", "displayName": "Product Card", "previews": [{"previewType": "staticMethod", "name": "DefaultPreview", "displayName": "<PERSON><PERSON><PERSON>", "autoGenerated": false}]}}]}, "previews-navigate-request": {"allOf": [{"$ref": "#/$defs/jsonrpc-request"}, {"properties": {"method": {"const": "previews/navigate"}, "params": {"type": "object", "properties": {"componentName": {"type": "string", "description": "Fully qualified name of the component", "minLength": 1}, "previewName": {"type": "string", "description": "Name of the preview to navigate to", "minLength": 1}}, "required": ["componentName", "previewName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 3, "method": "previews/navigate", "params": {"componentName": "MyApp.Views.ProductCard", "previewName": "DefaultPreview"}}]}, "previews-navigate-response": {"allOf": [{"$ref": "#/$defs/jsonrpc-response"}, {"properties": {"result": {"type": "null"}}}], "examples": [{"jsonrpc": "2.0", "id": 3, "result": null}]}, "previews-snapshot-request": {"allOf": [{"$ref": "#/$defs/jsonrpc-request"}, {"properties": {"method": {"const": "previews/snapshot"}, "params": {"type": "object", "properties": {"componentName": {"type": "string", "description": "Fully qualified name of the component", "minLength": 1}, "previewName": {"type": "string", "description": "Name of the preview to capture", "minLength": 1}}, "required": ["componentName", "previewName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 4, "method": "previews/snapshot", "params": {"componentName": "MyApp.Views.ProductCard", "previewName": "DefaultPreview"}}]}, "previews-snapshot-response": {"allOf": [{"$ref": "#/$defs/jsonrpc-response"}, {"properties": {"result": {"type": "string", "description": "Base64-encoded PNG image data", "pattern": "^[A-Za-z0-9+/]+={0,2}$", "minLength": 1}}}], "examples": [{"jsonrpc": "2.0", "id": 4, "result": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="}]}, "commands-list-request": {"allOf": [{"$ref": "#/$defs/jsonrpc-request"}, {"properties": {"method": {"const": "commands/list"}, "params": {"type": "object", "additionalProperties": false}}, "required": ["method"]}], "examples": [{"jsonrpc": "2.0", "id": 5, "method": "commands/list", "params": {}}]}, "commands-list-response": {"allOf": [{"$ref": "#/$defs/jsonrpc-response"}, {"properties": {"result": {"type": "array", "items": {"$ref": "#/$defs/PreviewCommandInfo"}}}}], "examples": [{"jsonrpc": "2.0", "id": 5, "result": [{"name": "MyApp.Commands.RefreshData", "displayName": "Refresh Data"}]}]}, "commands-get-request": {"allOf": [{"$ref": "#/$defs/jsonrpc-request"}, {"properties": {"method": {"const": "commands/get"}, "params": {"type": "object", "properties": {"commandName": {"type": "string", "description": "Fully qualified name of the command to retrieve", "minLength": 1}}, "required": ["commandName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 6, "method": "commands/get", "params": {"commandName": "MyApp.Commands.RefreshData"}}]}, "commands-get-response": {"allOf": [{"$ref": "#/$defs/jsonrpc-response"}, {"properties": {"result": {"oneOf": [{"$ref": "#/$defs/PreviewCommandInfo"}, {"type": "null"}]}}}], "examples": [{"jsonrpc": "2.0", "id": 6, "result": {"name": "MyApp.Commands.RefreshData", "displayName": "Refresh Data"}}]}, "commands-invoke-request": {"allOf": [{"$ref": "#/$defs/jsonrpc-request"}, {"properties": {"method": {"const": "commands/invoke"}, "params": {"type": "object", "properties": {"commandName": {"type": "string", "description": "Fully qualified name of the command to execute", "minLength": 1}}, "required": ["commandName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 7, "method": "commands/invoke", "params": {"commandName": "MyApp.Commands.RefreshData"}}]}, "commands-invoke-response": {"allOf": [{"$ref": "#/$defs/jsonrpc-response"}, {"properties": {"result": {"type": "null"}}}], "examples": [{"jsonrpc": "2.0", "id": 7, "result": null}]}, "registerApp-request": {"allOf": [{"$ref": "#/$defs/jsonrpc-request"}, {"properties": {"method": {"const": "registerApp"}, "params": {"type": "object", "properties": {"projectPath": {"type": "string", "description": "Full path to the project file (e.g., .csproj)", "minLength": 1}, "platformName": {"type": "string", "description": "Platform name (e.g., MAUI, WPF, etc.)", "minLength": 1}}, "required": ["projectPath", "platformName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 1, "method": "registerApp", "params": {"projectPath": "/projects/myProject/myProject.csproj", "platformName": "MAUI"}}]}, "registerApp-response": {"allOf": [{"$ref": "#/$defs/jsonrpc-response"}, {"properties": {"result": {"type": "null"}}}], "examples": [{"jsonrpc": "2.0", "id": 1, "result": null}]}, "notifications-components-listChanged-request": {"allOf": [{"$ref": "#/$defs/jsonrpc-request"}, {"properties": {"method": {"const": "notifications/components/listChanged"}, "params": {"type": "object", "additionalProperties": false}}, "required": ["method"]}], "examples": [{"jsonrpc": "2.0", "id": 2, "method": "notifications/components/listChanged", "params": {}}]}, "notifications-components-listChanged-response": {"allOf": [{"$ref": "#/$defs/jsonrpc-response"}, {"properties": {"result": {"type": "null"}}}], "examples": [{"jsonrpc": "2.0", "id": 2, "result": null}]}}, "oneOf": [{"$ref": "#/$defs/components-list-request"}, {"$ref": "#/$defs/components-list-response"}, {"$ref": "#/$defs/components-get-request"}, {"$ref": "#/$defs/components-get-response"}, {"$ref": "#/$defs/previews-navigate-request"}, {"$ref": "#/$defs/previews-navigate-response"}, {"$ref": "#/$defs/previews-snapshot-request"}, {"$ref": "#/$defs/previews-snapshot-response"}, {"$ref": "#/$defs/commands-list-request"}, {"$ref": "#/$defs/commands-list-response"}, {"$ref": "#/$defs/commands-get-request"}, {"$ref": "#/$defs/commands-get-response"}, {"$ref": "#/$defs/commands-invoke-request"}, {"$ref": "#/$defs/commands-invoke-response"}, {"$ref": "#/$defs/registerApp-request"}, {"$ref": "#/$defs/registerApp-response"}, {"$ref": "#/$defs/notifications-components-listChanged-request"}, {"$ref": "#/$defs/notifications-components-listChanged-response"}]}