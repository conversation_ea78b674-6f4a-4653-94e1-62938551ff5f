{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://hotpreview.dev/schemas/cpp-protocol.json", "title": "Component Preview Protocol (CPP) Schema", "description": "JSON Schema for the Component Preview Protocol - a cross-platform UI component preview system protocol", "type": "object", "definitions": {"components-list-request": {"allOf": [{"$ref": "#/definitions/jsonrpc-request"}, {"properties": {"method": {"const": "components/list"}, "params": {"type": "object", "additionalProperties": false}}, "required": ["method"]}], "examples": [{"jsonrpc": "2.0", "id": 1, "method": "components/list", "params": {}}]}, "components-list-response": {"allOf": [{"$ref": "#/definitions/jsonrpc-response"}, {"properties": {"result": {"type": "array", "items": {"$ref": "#/definitions/UIComponentInfo"}}}}], "examples": [{"jsonrpc": "2.0", "id": 1, "result": [{"name": "MyApp.Views.ProductCard", "uiComponentKind": "control", "displayName": "Product Card", "previews": [{"previewType": "staticMethod", "name": "DefaultPreview", "displayName": "<PERSON><PERSON><PERSON>", "autoGenerated": false}]}]}]}, "components-get-request": {"allOf": [{"$ref": "#/definitions/jsonrpc-request"}, {"properties": {"method": {"const": "components/get"}, "params": {"type": "object", "properties": {"componentName": {"type": "string", "description": "Fully qualified name of the component to retrieve", "minLength": 1}}, "required": ["componentName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 2, "method": "components/get", "params": {"componentName": "MyApp.Views.ProductCard"}}]}, "components-get-response": {"allOf": [{"$ref": "#/definitions/jsonrpc-response"}, {"properties": {"result": {"oneOf": [{"$ref": "#/definitions/UIComponentInfo"}, {"type": "null"}]}}}], "examples": [{"jsonrpc": "2.0", "id": 2, "result": {"name": "MyApp.Views.ProductCard", "uiComponentKind": "control", "displayName": "Product Card", "previews": [{"previewType": "staticMethod", "name": "DefaultPreview", "displayName": "<PERSON><PERSON><PERSON>", "autoGenerated": false}]}}]}, "previews-navigate-request": {"allOf": [{"$ref": "#/definitions/jsonrpc-request"}, {"properties": {"method": {"const": "previews/navigate"}, "params": {"type": "object", "properties": {"componentName": {"type": "string", "description": "Fully qualified name of the component", "minLength": 1}, "previewName": {"type": "string", "description": "Name of the preview to navigate to", "minLength": 1}}, "required": ["componentName", "previewName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 3, "method": "previews/navigate", "params": {"componentName": "MyApp.Views.ProductCard", "previewName": "DefaultPreview"}}]}, "previews-navigate-response": {"allOf": [{"$ref": "#/definitions/jsonrpc-response"}, {"properties": {"result": {"type": "null"}}}], "examples": [{"jsonrpc": "2.0", "id": 3, "result": null}]}, "previews-snapshot-request": {"allOf": [{"$ref": "#/definitions/jsonrpc-request"}, {"properties": {"method": {"const": "previews/snapshot"}, "params": {"type": "object", "properties": {"componentName": {"type": "string", "description": "Fully qualified name of the component", "minLength": 1}, "previewName": {"type": "string", "description": "Name of the preview to capture", "minLength": 1}}, "required": ["componentName", "previewName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 4, "method": "previews/snapshot", "params": {"componentName": "MyApp.Views.ProductCard", "previewName": "DefaultPreview"}}]}, "previews-snapshot-response": {"allOf": [{"$ref": "#/definitions/jsonrpc-response"}, {"properties": {"result": {"type": "string", "description": "Base64-encoded PNG image data", "pattern": "^[A-Za-z0-9+/]*={0,2}$"}}}], "examples": [{"jsonrpc": "2.0", "id": 4, "result": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="}]}, "commands-list-request": {"allOf": [{"$ref": "#/definitions/jsonrpc-request"}, {"properties": {"method": {"const": "commands/list"}, "params": {"type": "object", "additionalProperties": false}}, "required": ["method"]}], "examples": [{"jsonrpc": "2.0", "id": 5, "method": "commands/list", "params": {}}]}, "commands-list-response": {"allOf": [{"$ref": "#/definitions/jsonrpc-response"}, {"properties": {"result": {"type": "array", "items": {"$ref": "#/definitions/PreviewCommandInfo"}}}}], "examples": [{"jsonrpc": "2.0", "id": 5, "result": [{"name": "MyApp.Commands.RefreshData", "displayName": "Refresh Data"}]}]}, "commands-get-request": {"allOf": [{"$ref": "#/definitions/jsonrpc-request"}, {"properties": {"method": {"const": "commands/get"}, "params": {"type": "object", "properties": {"commandName": {"type": "string", "description": "Fully qualified name of the command to retrieve", "minLength": 1}}, "required": ["commandName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 6, "method": "commands/get", "params": {"commandName": "MyApp.Commands.RefreshData"}}]}, "commands-get-response": {"allOf": [{"$ref": "#/definitions/jsonrpc-response"}, {"properties": {"result": {"oneOf": [{"$ref": "#/definitions/PreviewCommandInfo"}, {"type": "null"}]}}}], "examples": [{"jsonrpc": "2.0", "id": 6, "result": {"name": "MyApp.Commands.RefreshData", "displayName": "Refresh Data"}}]}, "commands-invoke-request": {"allOf": [{"$ref": "#/definitions/jsonrpc-request"}, {"properties": {"method": {"const": "commands/invoke"}, "params": {"type": "object", "properties": {"commandName": {"type": "string", "description": "Fully qualified name of the command to execute", "minLength": 1}}, "required": ["commandName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 7, "method": "commands/invoke", "params": {"commandName": "MyApp.Commands.RefreshData"}}]}, "commands-invoke-response": {"allOf": [{"$ref": "#/definitions/jsonrpc-response"}, {"properties": {"result": {"type": "null"}}}], "examples": [{"jsonrpc": "2.0", "id": 7, "result": null}]}, "registerApp-request": {"allOf": [{"$ref": "#/definitions/jsonrpc-request"}, {"properties": {"method": {"const": "registerApp"}, "params": {"type": "object", "properties": {"projectPath": {"type": "string", "description": "Full path to the project file (e.g., .csproj)", "minLength": 1}, "platformName": {"type": "string", "description": "Platform name (e.g., MAUI, WPF, etc.)", "minLength": 1}}, "required": ["projectPath", "platformName"], "additionalProperties": false}}, "required": ["method", "params"]}], "examples": [{"jsonrpc": "2.0", "id": 1, "method": "registerApp", "params": {"projectPath": "/projects/myProject/myProject.csproj", "platformName": "MAUI"}}]}, "registerApp-response": {"allOf": [{"$ref": "#/definitions/jsonrpc-response"}, {"properties": {"result": {"type": "null"}}}], "examples": [{"jsonrpc": "2.0", "id": 1, "result": null}]}, "notifications-components-listChanged-request": {"allOf": [{"$ref": "#/definitions/jsonrpc-request"}, {"properties": {"method": {"const": "notifications/components/listChanged"}, "params": {"type": "object", "additionalProperties": false}}, "required": ["method"]}], "examples": [{"jsonrpc": "2.0", "id": 2, "method": "notifications/components/listChanged", "params": {}}]}, "notifications-components-listChanged-response": {"allOf": [{"$ref": "#/definitions/jsonrpc-response"}, {"properties": {"result": {"type": "null"}}}], "examples": [{"jsonrpc": "2.0", "id": 2, "result": null}]}}, "oneOf": [{"$ref": "#/definitions/components-list-request"}, {"$ref": "#/definitions/components-list-response"}, {"$ref": "#/definitions/components-get-request"}, {"$ref": "#/definitions/components-get-response"}, {"$ref": "#/definitions/previews-navigate-request"}, {"$ref": "#/definitions/previews-navigate-response"}, {"$ref": "#/definitions/previews-snapshot-request"}, {"$ref": "#/definitions/previews-snapshot-response"}, {"$ref": "#/definitions/commands-list-request"}, {"$ref": "#/definitions/commands-list-response"}, {"$ref": "#/definitions/commands-get-request"}, {"$ref": "#/definitions/commands-get-response"}, {"$ref": "#/definitions/commands-invoke-request"}, {"$ref": "#/definitions/commands-invoke-response"}, {"$ref": "#/definitions/registerApp-request"}, {"$ref": "#/definitions/registerApp-response"}, {"$ref": "#/definitions/notifications-components-listChanged-request"}, {"$ref": "#/definitions/notifications-components-listChanged-response"}]}