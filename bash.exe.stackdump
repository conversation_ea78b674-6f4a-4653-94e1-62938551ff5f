Stack trace:
Frame         Function      Args
0007FFFF9440  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8340) msys-2.0.dll+0x1FE8E
0007FFFF9440  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9718) msys-2.0.dll+0x67F9
0007FFFF9440  000210046832 (000210286019, 0007FFFF92F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9440  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9440  000210068E24 (0007FFFF9450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9720  00021006A225 (0007FFFF9450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBEE120000 ntdll.dll
7FFBEC5C0000 KERNEL32.DLL
7FFBEB510000 KERNELBASE.dll
7FFBED290000 USER32.dll
7FFBEBDE0000 win32u.dll
7FFBEC140000 GDI32.dll
7FFBEB910000 gdi32full.dll
7FFBEB2E0000 msvcp_win.dll
7FFBEBBD0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBEC170000 advapi32.dll
7FFBEDF80000 msvcrt.dll
7FFBEDED0000 sechost.dll
7FFBEC4A0000 RPCRT4.dll
7FFBEA8C0000 CRYPTBASE.DLL
7FFBEBE10000 bcryptPrimitives.dll
7FFBED680000 IMM32.DLL
